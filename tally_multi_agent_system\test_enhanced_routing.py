#!/usr/bin/env python3
"""
Enhanced Routing System Test
Tests the new intent-based routing, LaTeX generation, and Gemini integration
"""

import os
import sys
import json
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_intent_parsing():
    """Test the intent parsing functionality"""
    print("🧠 TESTING INTENT PARSING")
    print("=" * 50)
    
    try:
        from agent import parse_user_intent
        
        test_queries = [
            "Send an <NAME_EMAIL> about inventory drop",
            "Schedule a meeting tomorrow at 2 PM with the sales team",
            "Show me the financial trends for this quarter",
            "What are our current inventory levels?",
            "Analyze sales performance for top customers",
            "Generate a comprehensive business report with charts"
        ]
        
        for query in test_queries:
            print(f"\n📝 Query: {query}")
            intent_result = parse_user_intent(query)
            print(f"   Primary Intent: {intent_result['primary_intent']}")
            print(f"   All Intents: {intent_result['all_intents']}")
            print(f"   Multi-Agent: {intent_result['requires_multi_agent']}")
            if intent_result['entities']['emails']:
                print(f"   Emails Found: {intent_result['entities']['emails']}")
        
        print("\n✅ Intent parsing test completed")
        return True
        
    except Exception as e:
        print(f"❌ Intent parsing test failed: {e}")
        return False

def test_latex_generation():
    """Test LaTeX report generation"""
    print("\n📊 TESTING LATEX REPORT GENERATION")
    print("=" * 50)
    
    try:
        from agent import generate_latex_report, generate_chart_code
        
        # Sample multi-agent data
        sample_data = {
            'financial': {
                'summary': {
                    'total_revenue': 1175241.38,
                    'total_expenses': 1125214.82,
                    'net_profit': 50026.56,
                    'profit_margin': 4.26
                },
                'data': [
                    {'period': '2024-01', 'revenue': 400000},
                    {'period': '2024-02', 'revenue': 450000},
                    {'period': '2024-03', 'revenue': 325241}
                ]
            },
            'sales': {
                'summary': {
                    'total_sales': 1175241.38,
                    'customer_count': 33,
                    'avg_order_value': 12764
                },
                'top_customers': [
                    {'customer': 'ABC Corp', 'sales': 250000},
                    {'customer': 'XYZ Ltd', 'sales': 180000},
                    {'customer': 'DEF Inc', 'sales': 120000}
                ]
            },
            'inventory': {
                'summary': {
                    'total_items': 447,
                    'stock_value': -51027.56,
                    'low_stock_count': 436
                },
                'stock_details': [
                    {'item': 'Product A', 'quantity': 100},
                    {'item': 'Product B', 'quantity': 75},
                    {'item': 'Product C', 'quantity': 50}
                ]
            }
        }
        
        # Generate LaTeX report
        latex_content = generate_latex_report(sample_data, "Enhanced Multi-Agent Business Report")
        
        print(f"✅ LaTeX report generated ({len(latex_content)} characters)")
        print("📄 Report includes:")
        print("   - Financial analysis with trends chart")
        print("   - Sales analysis with customer chart")
        print("   - Inventory analysis with stock chart")
        print("   - Professional formatting")
        
        # Test chart generation
        chart_code = generate_chart_code('line', sample_data['financial']['data'], 'Revenue Trends', 'Month', 'Revenue (₹)')
        print(f"✅ Chart code generated ({len(chart_code)} characters)")
        
        return True
        
    except Exception as e:
        print(f"❌ LaTeX generation test failed: {e}")
        return False

def test_gemini_integration():
    """Test Gemini API integration"""
    print("\n🤖 TESTING GEMINI INTEGRATION")
    print("=" * 50)
    
    try:
        from agent import get_gemini_insights
        
        # Check if Gemini API key is configured
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("⚠️ GEMINI_API_KEY not configured - testing disabled mode")
            
        sample_data = {
            'financial': {'summary': {'profit_margin': 4.26, 'revenue': 1175241}},
            'sales': {'summary': {'customer_count': 33, 'avg_order_value': 12764}},
            'inventory': {'summary': {'low_stock_count': 436, 'total_items': 447}}
        }
        
        insights_result = get_gemini_insights(sample_data, "Analyze business performance")
        
        if insights_result['status'] == 'success':
            print("✅ Gemini insights generated successfully")
            print(f"   Insights: {len(insights_result['insights']['insights'])} items")
            print(f"   Recommendations: {len(insights_result['insights']['recommendations'])} items")
        elif insights_result['status'] == 'disabled':
            print("⚠️ Gemini API disabled (expected without API key)")
        else:
            print(f"❌ Gemini integration failed: {insights_result.get('error_message', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini integration test failed: {e}")
        return False

def test_enhanced_workflow():
    """Test the complete enhanced workflow"""
    print("\n🔄 TESTING ENHANCED WORKFLOW")
    print("=" * 50)
    
    try:
        # Test that the main agent has all the new tools
        from agent import agent
        
        # Get tool names - handle different tool object structures
        tool_names = []
        for tool in agent.tools:
            if hasattr(tool, 'function') and hasattr(tool.function, 'name'):
                tool_names.append(tool.function.name)
            elif hasattr(tool, 'name'):
                tool_names.append(tool.name)
            elif hasattr(tool, '_function') and hasattr(tool._function, '__name__'):
                tool_names.append(tool._function.__name__)
            else:
                tool_names.append(str(tool))
        expected_tools = [
            'get_tally_data',
            'parse_user_intent',
            'generate_latex_report',
            'compile_and_email_report',
            'get_gemini_insights',
            'schedule_calendar_event'
        ]
        
        print("🛠️ Available tools:")
        for tool in tool_names:
            status = "✅" if tool in expected_tools else "❓"
            print(f"   {status} {tool}")
        
        missing_tools = set(expected_tools) - set(tool_names)
        if missing_tools:
            print(f"❌ Missing tools: {missing_tools}")
            return False
        
        print("✅ All expected tools are available")
        
        # Test that sub-agents are loaded
        if hasattr(agent, 'sub_agents') and agent.sub_agents:
            print(f"✅ Sub-agents loaded: {len(agent.sub_agents)} agents")
        else:
            print("❌ Sub-agents not loaded properly")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced workflow test failed: {e}")
        return False

def main():
    """Run all enhanced routing tests"""
    print("🚀 ENHANCED MULTI-AGENT SYSTEM TESTING")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Intent Parsing", test_intent_parsing),
        ("LaTeX Generation", test_latex_generation),
        ("Gemini Integration", test_gemini_integration),
        ("Enhanced Workflow", test_enhanced_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 30)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 OVERALL: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Enhanced system ready!")
        return 0
    else:
        print("⚠️ Some tests failed - review issues above")
        return 1

if __name__ == "__main__":
    exit(main())
