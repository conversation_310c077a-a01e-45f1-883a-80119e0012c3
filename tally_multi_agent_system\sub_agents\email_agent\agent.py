"""
Email Agent - Specialized in email communication with dynamic data fetching
Google ADK compliant sub-agent that delegates to other agents for real-time data
"""

import os
import sys
import re
import smtplib
import math
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool

def clean_nan_values(obj):
    """
    Recursively clean NaN values from data structures to ensure JSON compatibility.
    Converts NaN to None (null in JSON), which is valid JSON.
    """
    if isinstance(obj, float):
        if math.isnan(obj) or math.isinf(obj):
            return None
        return obj
    elif isinstance(obj, dict):
        return {k: clean_nan_values(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [clean_nan_values(item) for item in obj]
    else:
        return obj

def parse_email_instruction(instruction: str) -> Dict[str, Any]:
    """
    Parse user instruction to extract email recipient, subject, and required data types.
    
    Args:
        instruction: User instruction containing email details and business query
    
    Returns:
        Dictionary containing parsed email information and data requirements
    """
    try:
        result = {
            'recipient': None,
            'subject': None,
            'body_requirements': [],
            'data_needed': {
                'financial': False,
                'sales': False,
                'inventory': False,
                'purchase': False,
                'analytics': False
            },
            'specific_queries': []
        }
        
        # Extract email using regex
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_match = re.search(email_pattern, instruction)
        if email_match:
            result['recipient'] = email_match.group()
        
        # Extract subject
        subject_patterns = [
            r'[Ss]ubject:\s*([^\n\r]+)',
            r'[Ss]ubject\s*:\s*([^\n\r]+)',
            r'about\s+([^,\n\r]+)',
            r'regarding\s+([^,\n\r]+)'
        ]
        
        for pattern in subject_patterns:
            subject_match = re.search(pattern, instruction)
            if subject_match:
                result['subject'] = subject_match.group(1).strip()
                break
        
        # If no subject found, generate from content
        if not result['subject']:
            if 'profit' in instruction.lower():
                result['subject'] = "Business Analysis: Profit Analysis"
            elif 'sales' in instruction.lower():
                result['subject'] = "Business Analysis: Sales Report"
            elif 'inventory' in instruction.lower():
                result['subject'] = "Business Analysis: Inventory Report"
            else:
                result['subject'] = "Business Analysis Report"
        
        # Analyze what data is needed based on keywords
        instruction_lower = instruction.lower()
        
        # Financial data keywords
        financial_keywords = ['profit', 'margin', 'financial', 'balance', 'revenue', 'expense', 'cash flow', 'p&l', 'profit and loss']
        if any(keyword in instruction_lower for keyword in financial_keywords):
            result['data_needed']['financial'] = True
            result['specific_queries'].append('financial_analysis')
        
        # Sales data keywords
        sales_keywords = ['sales', 'customer', 'revenue', 'transaction', 'selling', 'orders']
        if any(keyword in instruction_lower for keyword in sales_keywords):
            result['data_needed']['sales'] = True
            result['specific_queries'].append('sales_analysis')
        
        # Inventory data keywords
        inventory_keywords = ['inventory', 'stock', 'items', 'products', 'warehouse', 'goods']
        if any(keyword in instruction_lower for keyword in inventory_keywords):
            result['data_needed']['inventory'] = True
            result['specific_queries'].append('inventory_analysis')
        
        # Purchase data keywords
        purchase_keywords = ['purchase', 'supplier', 'vendor', 'buying', 'procurement']
        if any(keyword in instruction_lower for keyword in purchase_keywords):
            result['data_needed']['purchase'] = True
            result['specific_queries'].append('purchase_analysis')
        
        # Analytics keywords
        analytics_keywords = ['trend', 'forecast', 'analysis', 'pattern', 'correlation', 'performance']
        if any(keyword in instruction_lower for keyword in analytics_keywords):
            result['data_needed']['analytics'] = True
            result['specific_queries'].append('analytics_insights')
        
        # Extract body requirements
        body_match = re.search(r'[Bb]ody:\s*([^\n\r]+)', instruction)
        if body_match:
            body_text = body_match.group(1).strip()
            result['body_requirements'].append(body_text)
        
        return clean_nan_values(result)
        
    except Exception as e:
        return {
            'status': 'error',
            'error_message': f"Failed to parse email instruction: {str(e)}",
            'recipient': None,
            'subject': 'Business Analysis Report',
            'data_needed': {},  # No hardcoded data requirements
            'specific_queries': []
        }

def delegate_to_agent(agent_type: str, query_type: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Delegate data fetching to appropriate specialized agents.
    
    Args:
        agent_type: Type of agent ('financial', 'sales', 'inventory', 'purchase', 'analytics')
        query_type: Specific query type for the agent
        params: Additional parameters for the query
    
    Returns:
        Dictionary containing the agent's response data
    """
    try:
        if params is None:
            params = {}
        
        # Set default date range for queries
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')  # Last 3 months
        
        if agent_type == "financial":
            from sub_agents.financial_agent.agent import get_account_balance

            # Get financial data (account balance)
            financial_result = get_account_balance("Cash")
            financial_result['agent_type'] = 'financial'
            return financial_result
            
        elif agent_type == "sales":
            from sub_agents.sales_agent.agent import get_sales_summary
            from datetime import datetime, timedelta

            # Get dynamic date range (last 3 months or from params)
            if params and 'start_date' in params and 'end_date' in params:
                start_date = params['start_date']
                end_date = params['end_date']
            else:
                # Default to last 3 months
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')

            sales_result = get_sales_summary(start_date, end_date)
            sales_result['agent_type'] = 'sales'
            sales_result['date_range'] = {'start': start_date, 'end': end_date}
            return sales_result

        elif agent_type == "inventory":
            from sub_agents.inventory_agent.agent import get_stock_summary

            # Get inventory summary with optional filters
            item_filter = params.get('item_name') if params else None
            inventory_result = get_stock_summary(item_name=item_filter)
            inventory_result['agent_type'] = 'inventory'
            return inventory_result

        elif agent_type == "purchase":
            from sub_agents.purchase_agent.agent import get_purchase_summary
            from datetime import datetime, timedelta

            # Get dynamic date range (last 3 months or from params)
            if params and 'start_date' in params and 'end_date' in params:
                start_date = params['start_date']
                end_date = params['end_date']
            else:
                # Default to last 3 months
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')

            purchase_result = get_purchase_summary(start_date, end_date)
            purchase_result['agent_type'] = 'purchase'
            purchase_result['date_range'] = {'start': start_date, 'end': end_date}
            return purchase_result

        elif agent_type == "analytics":
            from sub_agents.analytics_agent.agent import get_business_dashboard

            # Get business dashboard
            analytics_result = get_business_dashboard()
            analytics_result['agent_type'] = 'analytics'
            return analytics_result
        
        else:
            return {
                'agent_type': agent_type,
                'status': 'error',
                'error_message': f"Unknown agent type: {agent_type}"
            }
            
    except Exception as e:
        return {
            'agent_type': agent_type,
            'status': 'error',
            'error_message': f"Failed to delegate to {agent_type} agent: {str(e)}"
        }

def compose_email_content(parsed_instruction: Dict[str, Any], agent_responses: List[Dict[str, Any]]) -> str:
    """
    Compose email content using real data from agent responses.
    
    Args:
        parsed_instruction: Parsed email instruction with requirements
        agent_responses: List of responses from delegated agents
    
    Returns:
        Formatted email content string
    """
    try:
        # Start with greeting
        content = "Dear Recipient,\n\n"
        
        # Add introduction based on subject
        subject = parsed_instruction.get('subject', 'Business Analysis Report')
        if 'profit' in subject.lower():
            content += "As requested, we have conducted a detailed profit margin analysis. "
        elif 'sales' in subject.lower():
            content += "As requested, we have prepared a comprehensive sales performance report. "
        elif 'inventory' in subject.lower():
            content += "As requested, we have analyzed the current inventory status. "
        else:
            content += f"As requested, we have conducted a detailed analysis regarding: {subject.lower()}. "
        
        content += "Here are the key insights derived from our real-time data analysis:\n\n"
        
        # Process each agent response (Fixed to use actual data structures)
        for response in agent_responses:
            if response.get('status') in ['success', 'partial']:
                agent_type = response.get('agent_type', 'unknown')

                if agent_type == 'financial':
                    content += "FINANCIAL OVERVIEW:\n"
                    content += "=" * 50 + "\n"

                    # Account balance from summary
                    if 'summary' in response:
                        summary = response['summary']
                        if 'balance' in summary:
                            content += f"• Current Cash Balance: ₹{summary['balance']:,.2f}\n"
                        if 'transaction_count' in summary:
                            content += f"• Total Transactions: {summary['transaction_count']:,}\n"
                        if 'last_activity' in summary:
                            content += f"• Last Activity: {summary['last_activity']}\n"

                    content += "\n"

                elif agent_type == 'sales':
                    content += "SALES PERFORMANCE:\n"
                    content += "=" * 50 + "\n"

                    # Sales summary (Fixed to use actual structure)
                    if 'summary' in response:
                        summary = response['summary']
                        if 'total_sales' in summary:
                            content += f"• Total Sales Revenue: ₹{summary['total_sales']:,.2f}\n"
                        if 'total_transactions' in summary:
                            content += f"• Total Transactions: {summary['total_transactions']:,}\n"
                        if 'average_order_value' in summary:
                            content += f"• Average Order Value: ₹{summary['average_order_value']:,.2f}\n"
                        if 'total_customers' in summary:
                            content += f"• Total Customers: {summary['total_customers']:,}\n"
                        if 'top_customer' in summary:
                            content += f"• Top Customer: {summary['top_customer']}\n"

                    content += "\n"

                elif agent_type == 'inventory':
                    content += "INVENTORY STATUS:\n"
                    content += "=" * 50 + "\n"

                    # Inventory summary (Fixed to use actual structure)
                    if 'summary' in response:
                        summary = response['summary']
                        if 'total_items' in summary:
                            content += f"• Total Items in Inventory: {summary['total_items']:,}\n"
                        if 'total_stock_value' in summary:
                            content += f"• Total Inventory Value: ₹{summary['total_stock_value']:,.2f}\n"
                        if 'low_stock_items' in summary:
                            content += f"• Items Requiring Attention: {summary['low_stock_items']:,}\n"
                        if 'categories' in summary:
                            content += f"• Product Categories: {summary['categories']:,}\n"

                    content += "\n"

                elif agent_type == 'analytics':
                    content += "BUSINESS ANALYTICS:\n"
                    content += "=" * 50 + "\n"

                    # Analytics dashboard (Fixed to use actual structure)
                    if 'dashboard' in response:
                        dashboard = response['dashboard']
                        if 'revenue' in dashboard:
                            content += f"• Total Revenue: ₹{dashboard['revenue']:,.2f}\n"
                        if 'gross_profit' in dashboard:
                            content += f"• Gross Profit: ₹{dashboard['gross_profit']:,.2f}\n"
                        if 'profit_margin' in dashboard:
                            content += f"• Profit Margin: {dashboard['profit_margin']:.2f}%\n"
                        if 'net_cash_flow' in dashboard:
                            content += f"• Net Cash Flow: ₹{dashboard['net_cash_flow']:,.2f}\n"

                        # Top products
                        if 'top_products' in dashboard and dashboard['top_products']:
                            content += "• Top Selling Products:\n"
                            for i, product in enumerate(dashboard['top_products'][:3], 1):
                                name = product.get('product_name', 'Unknown')
                                units = product.get('units_sold', 0)
                                revenue = product.get('revenue', 0)
                                content += f"  {i}. {name}: {units} units (₹{revenue:,.2f})\n"

                    content += "\n"
        
        # Add analysis and recommendations
        content += "KEY INSIGHTS & RECOMMENDATIONS:\n"
        content += "=" * 50 + "\n"
        
        # Analyze the data for insights (Fixed to use actual data structures)
        analytics_data = next((r for r in agent_responses if r.get('agent_type') == 'analytics'), {})
        sales_data = next((r for r in agent_responses if r.get('agent_type') == 'sales'), {})
        inventory_data = next((r for r in agent_responses if r.get('agent_type') == 'inventory'), {})

        # Generate insights based on actual data
        if analytics_data.get('dashboard', {}).get('profit_margin', 0) < 10:
            margin = analytics_data['dashboard']['profit_margin']
            content += f"• ⚠️ Profit margin is {margin:.2f}% - consider cost optimization strategies\n"

        if inventory_data.get('summary', {}).get('low_stock_items', 0) > 0:
            low_stock = inventory_data['summary']['low_stock_items']
            content += f"• 📦 {low_stock} items require immediate restocking attention\n"

        if sales_data.get('summary', {}).get('total_transactions', 0) > 0:
            transactions = sales_data['summary']['total_transactions']
            content += f"• 📈 Sales data shows active business operations with {transactions:,} transactions\n"

        if analytics_data.get('dashboard', {}).get('net_cash_flow', 0) < 0:
            cash_flow = analytics_data['dashboard']['net_cash_flow']
            content += f"• 💰 Negative cash flow of ₹{abs(cash_flow):,.2f} requires attention\n"
        
        content += "\nThis analysis is based on real-time data extracted from your Tally ERP system.\n"
        content += f"Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        content += "Best regards,\n"
        content += "Tally Multi-Agent Analysis System"
        
        return content
        
    except Exception as e:
        return f"""Dear Recipient,

We encountered an issue while generating your requested analysis report.

Error: {str(e)}

Please contact the system administrator for assistance.

Best regards,
Tally Multi-Agent Analysis System"""

def send_business_email(instruction: str, actually_send: bool = True) -> Dict[str, Any]:
    """
    Main function to process email requests with dynamic data fetching.
    
    Args:
        instruction: User instruction for email sending
        actually_send: Whether to actually send the email or just generate content
    
    Returns:
        Dictionary containing email processing results
    """
    try:
        print(f"📧 Processing email request: {instruction}")
        
        # Step 1: Parse the instruction
        parsed = parse_email_instruction(instruction)
        print(f"📋 Parsed instruction: {parsed}")
        
        if not parsed.get('recipient'):
            return {
                'status': 'error',
                'error_message': 'No valid email recipient found in instruction'
            }
        
        # Step 2: Identify required agents and delegate
        agent_responses = []
        data_needed = parsed.get('data_needed', {})
        
        print(f"🔍 Data requirements: {data_needed}")
        
        # Delegate to each required agent
        for agent_type, is_needed in data_needed.items():
            if is_needed:
                print(f"🤖 Delegating to {agent_type} agent...")
                response = delegate_to_agent(agent_type, f"{agent_type}_analysis")
                agent_responses.append(response)
                print(f"✅ {agent_type} agent response: {response.get('status', 'unknown')}")
        
        # Step 3: Wait for all responses (they're synchronous, so already complete)
        print(f"📊 Collected {len(agent_responses)} agent responses")
        
        # Step 4: Compose email with real data
        email_content = compose_email_content(parsed, agent_responses)
        
        # Step 5: Send email if requested
        email_details = {
            'recipient': parsed['recipient'],
            'subject': parsed['subject'],
            'content': email_content,
            'timestamp': datetime.now().isoformat()
        }
        
        if actually_send:
            # Send actual email
            send_result = send_actual_email(
                parsed['recipient'],
                parsed['subject'],
                email_content
            )
            
            if send_result.get('status') == 'success':
                return {
                    'status': 'success',
                    'message': f'Email sent successfully to {parsed["recipient"]}',
                    'email_details': email_details,
                    'agent_responses_count': len(agent_responses),
                    'data_sources': [r.get('agent_type') for r in agent_responses if r.get('status') in ['success', 'partial']]
                }
            else:
                return {
                    'status': 'error',
                    'error_message': f'Failed to send email: {send_result.get("error_message")}',
                    'email_details': email_details
                }
        else:
            return {
                'status': 'success',
                'message': 'Email content generated successfully (not sent)',
                'email_details': email_details,
                'agent_responses_count': len(agent_responses),
                'data_sources': [r.get('agent_type') for r in agent_responses if r.get('status') in ['success', 'partial']]
            }
        
    except Exception as e:
        return {
            'status': 'error',
            'error_message': f'Failed to process email request: {str(e)}'
        }

def send_actual_email(recipient: str, subject: str, content: str) -> Dict[str, Any]:
    """
    Send actual email using SMTP with real credentials.

    Args:
        recipient: Email recipient
        subject: Email subject
        content: Email content

    Returns:
        Dictionary containing send results
    """
    try:
        # Import email configuration
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from email_config import get_email_config

        config = get_email_config()

        # Create message
        message = MIMEMultipart()
        message["From"] = config['sender_email']
        message["To"] = recipient
        message["Subject"] = subject

        # Add body to email
        message.attach(MIMEText(content, "plain"))

        # Create SMTP session
        print(f"📧 Connecting to {config['smtp_server']}:{config['smtp_port']}")
        server = smtplib.SMTP(config['smtp_server'], config['smtp_port'], timeout=config['timeout'])

        if config['use_tls']:
            server.starttls()  # Enable security
            print("✅ TLS connection established")

        # Authenticate
        print("🔐 Authenticating...")
        server.login(config['sender_email'], config['sender_password'])
        print("✅ Authentication successful")

        # Send email
        print(f"📤 Sending email to: {recipient}")
        text = message.as_string()
        server.sendmail(config['sender_email'], recipient, text)
        server.quit()

        print("✅ Email sent successfully!")

        return {
            'status': 'success',
            'message': f'Email sent successfully to {recipient}',
            'recipient': recipient,
            'subject': subject,
            'sender': config['sender_email'],
            'timestamp': datetime.now().isoformat()
        }

    except Exception as e:
        error_msg = str(e)
        print(f"❌ Email sending failed: {error_msg}")

        return {
            'status': 'error',
            'error_message': f'Failed to send email: {error_msg}',
            'recipient': recipient,
            'subject': subject,
            'timestamp': datetime.now().isoformat()
        }

# Create tools for the Email Agent
parse_email_tool = FunctionTool(parse_email_instruction)
delegate_tool = FunctionTool(delegate_to_agent)
compose_email_tool = FunctionTool(compose_email_content)
send_email_tool = FunctionTool(send_business_email)
send_actual_email_tool = FunctionTool(send_actual_email)

# Create the Email Agent
agent = LlmAgent(
    name="EmailAgent",
    model="gemini-2.0-flash",
    description="""
    Specialized Email Agent for business communication with dynamic data integration.
    
    This agent:
    1. Parses email requests to understand recipient and data requirements
    2. Delegates to appropriate specialized agents (Financial, Sales, Inventory, Purchase, Analytics)
    3. Waits for real-time data responses from all required agents
    4. Composes comprehensive emails using actual business data
    5. Sends emails with current, accurate information
    
    NEVER uses static or hardcoded data. Always fetches real-time data from other agents.
    """,
    instruction="""
    You are the EmailAgent, responsible for sending business emails with real-time data.

    Your workflow:
    1. Parse the user's email request using parse_email_instruction
    2. Identify what data is needed (financial, sales, inventory, etc.)
    3. Delegate to appropriate agents using delegate_to_agent for each data type
    4. Wait for all agent responses with real data
    5. Compose email content using compose_email_content with the real data
    6. Send the email using send_business_email

    CRITICAL RULES:
    - NEVER use static, hardcoded, or sample data
    - ALWAYS delegate to other agents for real-time data
    - WAIT for all agent responses before composing email
    - Include actual numbers, dates, and business metrics from the agents
    - Provide meaningful analysis based on the real data received

    For requests like "analyze profit margins", delegate to FinancialAgent for real profit data.
    For requests mentioning "sales trends", delegate to SalesAgent for actual sales data.
    For requests about "inventory", delegate to InventoryAgent for current stock data.

    Always use the send_business_email function as your primary tool.
    """,
    tools=[parse_email_tool, delegate_tool, compose_email_tool, send_email_tool, send_actual_email_tool]
)