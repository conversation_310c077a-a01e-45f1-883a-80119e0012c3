"""
Tally Multi-Agent System - Root Agent
Google ADK compliant root agent for comprehensive Tally ERP data analysis
"""

import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import sqlite3
import math
import pandas as pd
import numpy as np

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool

def clean_nan_values(obj):
    """
    Recursively clean NaN values from data structures to ensure JSON compatibility.
    Converts NaN to None (null in JSON), which is valid JSON.
    """
    
    if isinstance(obj, float):
        if math.isnan(obj) or math.isinf(obj):
            return None
        return obj
    elif isinstance(obj, dict):
        return {k: clean_nan_values(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [clean_nan_values(item) for item in obj]
    elif isinstance(obj, pd.Series):
        return clean_nan_values(obj.tolist())
    elif isinstance(obj, pd.DataFrame):
        # Replace NaN values with None before converting to dict
        df_cleaned = obj.replace([np.nan, np.inf, -np.inf], None)
        return clean_nan_values(df_cleaned.to_dict('records'))
    elif isinstance(obj, np.ndarray):
        return clean_nan_values(obj.tolist())
    elif obj is np.nan or (isinstance(obj, float) and pd.isna(obj)) or pd.isna(obj):
        return None
    else:
        return obj

# Database connection tool
def get_tally_data(query_type: str, table_name: Optional[str] = None, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Get data from Tally database based on query type and filters.
    
    Args:
        query_type: Type of query (balance, transactions, inventory, etc.)
        table_name: Specific table to query
        filters: Additional filters for the query
    
    Returns:
        Dictionary containing query results
    """
    import pandas as pd
    
    try:
        db_path = os.path.join(os.path.dirname(__file__), '..', 'Data', 'tallydb.db')
        conn = sqlite3.connect(db_path)
        
        # Define common queries based on query_type
        if query_type == "account_balance":
            query = """
            SELECT 
                l.name as account_name,
                l.parent,
                SUM(a.amount) as balance
            FROM mst_ledger l
            LEFT JOIN trn_accounting a ON l.name = a.ledger
            WHERE l.name LIKE ?
            GROUP BY l.name, l.parent
            """
            account_name = filters.get('account_name', '%') if filters else '%'
            df = pd.read_sql_query(query, conn, params=[f"%{account_name}%"])
            
        elif query_type == "cash_flow":
            query = """
            SELECT 
                v.date,
                v.voucher_type,
                a.ledger,
                a.amount,
                v.party_name
            FROM trn_voucher v
            JOIN trn_accounting a ON v.guid = a.guid
            WHERE v.date BETWEEN ? AND ?
            AND (a.ledger LIKE '%Cash%' OR a.ledger LIKE '%Bank%')
            ORDER BY v.date
            """
            # Use dynamic date range - default to current fiscal year or last 12 months
            from datetime import datetime, timedelta
            if filters:
                start_date = filters.get('start_date')
                end_date = filters.get('end_date')
            else:
                start_date = None
                end_date = None

            if not start_date:
                # Default to last 12 months
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
            elif not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            df = pd.read_sql_query(query, conn, params=[start_date, end_date])
            
        elif query_type == "inventory_summary":
            query = """
            SELECT 
                si.name as item_name,
                si.parent as category,
                si.base_unit,
                COALESCE(SUM(i.quantity), 0) as total_quantity,
                COALESCE(AVG(i.rate), 0) as avg_rate
            FROM mst_stock_item si
            LEFT JOIN trn_inventory i ON si.name = i.item
            GROUP BY si.name, si.parent, si.base_unit
            """
            df = pd.read_sql_query(query, conn)
            
        elif query_type == "profit_loss":
            query = """
            SELECT 
                l.parent,
                l.name as account_name,
                SUM(a.amount) as amount
            FROM mst_ledger l
            JOIN trn_accounting a ON l.name = a.ledger
            WHERE l.parent IN ('Sales Accounts', 'Purchase Accounts', 'Direct Expenses', 'Indirect Expenses')
            GROUP BY l.parent, l.name
            ORDER BY l.parent, l.name
            """
            df = pd.read_sql_query(query, conn)
            
        else:
            # Generic table query
            if table_name:
                query = f"SELECT * FROM {table_name} LIMIT 100"
                df = pd.read_sql_query(query, conn)
            else:
                df = pd.DataFrame()
        
        conn.close()
        
        # Convert DataFrame to dictionary and clean NaN values
        result = {
            "status": "success",
            "query_type": query_type,
            "data": clean_nan_values(df.to_dict('records')) if not df.empty else [],
            "row_count": len(df),
            "columns": df.columns.tolist() if not df.empty else []
        }
        
        return clean_nan_values(result)
        
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "query_type": query_type
        }

def schedule_calendar_event(title: str, date_time: str, duration_minutes: int = 60, participants: str = "", location: str = "") -> Dict[str, Any]:
    """
    Schedule a calendar event using natural language.

    Args:
        title: Event title/subject
        date_time: Date and time in natural language (e.g., "August 5th at 11:45 AM", "tomorrow at 2 PM")
        duration_minutes: Duration in minutes (default: 60)
        participants: Comma-separated list of participant names or emails
        location: Event location (optional)

    Returns:
        Dictionary containing event creation results with confirmation
    """
    try:
        # Import calendar agent function
        from sub_agents.calendar_agent.agent import schedule_meeting_from_text

        # Build natural language text for the calendar agent
        text_parts = [f"Schedule {title}"]

        if date_time:
            text_parts.append(f"on {date_time}")

        if duration_minutes != 60:
            if duration_minutes == 30:
                text_parts.append("for 30 minutes")
            elif duration_minutes == 90:
                text_parts.append("for 1.5 hours")
            elif duration_minutes == 120:
                text_parts.append("for 2 hours")
            else:
                text_parts.append(f"for {duration_minutes} minutes")

        if participants:
            text_parts.append(f"with {participants}")

        if location:
            text_parts.append(f"at {location}")

        natural_language_text = " ".join(text_parts)

        # Call the calendar agent
        result = schedule_meeting_from_text(natural_language_text)

        # Add confirmation message
        if result.get('status') == 'success':
            event_details = result.get('event_details', {})
            confirmation = f"""
✅ **Calendar Event Successfully Created!**

📅 **Event:** {event_details.get('title', title)}
⏰ **Time:** {event_details.get('start_time', date_time)}
⏱️ **Duration:** {event_details.get('duration', f'{duration_minutes} minutes')}
👥 **Participants:** {', '.join(event_details.get('participants', [participants])) if event_details.get('participants') else participants or 'None'}
📍 **Location:** {event_details.get('location', location) or 'Not specified'}
🔔 **Reminder:** {event_details.get('reminder_set', '30 minutes before')}
🔗 **Calendar Link:** {event_details.get('event_link', 'Available in Google Calendar')}

The meeting has been added to your Google Calendar and invitations will be sent to participants if email addresses were provided.
"""
            result['confirmation_message'] = confirmation

        elif result.get('status') == 'simulated':
            confirmation = f"""
⚠️ **Calendar Event Simulated (Setup Required)**

📅 **Event:** {title}
⏰ **Requested Time:** {date_time}
⏱️ **Duration:** {duration_minutes} minutes
👥 **Participants:** {participants or 'None'}

To enable actual calendar creation, complete the Google Calendar API setup by running the authentication flow.
"""
            result['confirmation_message'] = confirmation

        else:
            error_msg = result.get('error_message', 'Unknown error occurred')
            result['confirmation_message'] = f"❌ **Failed to create calendar event:** {error_msg}"

        return result

    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "confirmation_message": f"❌ **Calendar scheduling failed:** {str(e)}"
        }



def parse_user_intent(user_query: str) -> Dict[str, Any]:
    """
    Parse user query to determine intent and required agents.

    Args:
        user_query: The user's natural language query

    Returns:
        Dictionary containing intent analysis and routing information
    """
    import re

    query_lower = user_query.lower()

    # Intent categories and keywords
    intent_patterns = {
        'email': ['email', 'send email', 'mail', 'send mail', 'compose email', 'send to'],
        'calendar': ['schedule', 'meeting', 'appointment', 'calendar', 'book', 'event'],
        'financial': ['financial', 'profit', 'loss', 'balance', 'cash flow', 'revenue', 'expenses', 'p&l', 'balance sheet'],
        'inventory': ['inventory', 'stock', 'items', 'products', 'warehouse', 'reorder', 'aging'],
        'sales': ['sales', 'customers', 'orders', 'revenue', 'selling', 'customer analysis'],
        'purchase': ['purchase', 'suppliers', 'procurement', 'buying', 'vendor', 'supplier analysis'],
        'analytics': ['analytics', 'dashboard', 'trends', 'forecast', 'prediction', 'correlation', 'analysis']
    }

    # Detect primary intent
    detected_intents = []
    for intent, keywords in intent_patterns.items():
        for keyword in keywords:
            if keyword in query_lower:
                detected_intents.append(intent)
                break

    # Remove duplicates while preserving order
    detected_intents = list(dict.fromkeys(detected_intents))

    # Determine primary intent (first detected or most specific)
    primary_intent = detected_intents[0] if detected_intents else 'general'

    # Extract entities (dates, amounts, names)
    entities = {
        'dates': re.findall(r'\b\d{4}-\d{2}-\d{2}\b|\b\d{1,2}/\d{1,2}/\d{4}\b', user_query),
        'emails': re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', user_query),
        'amounts': re.findall(r'₹[\d,]+|Rs\.?\s*[\d,]+|\$[\d,]+', user_query),
        'periods': re.findall(r'\b(monthly|quarterly|yearly|daily|weekly)\b', query_lower)
    }

    return {
        'primary_intent': primary_intent,
        'all_intents': detected_intents,
        'entities': entities,
        'requires_multi_agent': len(detected_intents) > 1,
        'original_query': user_query
    }

def generate_chart_code(chart_type: str, data: List[Dict[str, Any]], title: str, x_label: str = "", y_label: str = "") -> str:
    """
    Generate TikZ/PGFPlots code for charts based on data.

    Args:
        chart_type: Type of chart ('line', 'bar', 'pie')
        data: Data points for the chart
        title: Chart title
        x_label: X-axis label
        y_label: Y-axis label

    Returns:
        LaTeX TikZ code for the chart
    """
    if not data:
        return f"% No data available for {title}"

    if chart_type == "line":
        coordinates = []
        for i, point in enumerate(data[:10]):  # Limit to 10 points
            if isinstance(point, dict):
                x_val = i + 1
                y_val = 0
                # Try to find numeric values
                for key, value in point.items():
                    if isinstance(value, (int, float)) and value != 0:
                        y_val = value
                        break
                coordinates.append(f"({x_val},{y_val})")

        coord_str = " ".join(coordinates)
        return f"""
\\begin{{tikzpicture}}
\\begin{{axis}}[
    title={{{title}}},
    xlabel={{{x_label}}},
    ylabel={{{y_label}}},
    width=10cm,
    height=6cm,
    grid=major
]
\\addplot coordinates {{{coord_str}}};
\\end{{axis}}
\\end{{tikzpicture}}
"""

    elif chart_type == "bar":
        bars = []
        for i, point in enumerate(data[:8]):  # Limit to 8 bars
            if isinstance(point, dict):
                y_val = 0
                label = f"Item {i+1}"
                # Try to find numeric values and labels
                for key, value in point.items():
                    if isinstance(value, (int, float)) and value != 0:
                        y_val = value
                    elif isinstance(value, str) and len(value) < 20:
                        label = value[:15]  # Truncate long labels
                bars.append(f"({i+1},{y_val})")

        coord_str = " ".join(bars)
        return f"""
\\begin{{tikzpicture}}
\\begin{{axis}}[
    title={{{title}}},
    xlabel={{{x_label}}},
    ylabel={{{y_label}}},
    width=10cm,
    height=6cm,
    ybar,
    grid=major
]
\\addplot coordinates {{{coord_str}}};
\\end{{axis}}
\\end{{tikzpicture}}
"""

    else:  # Default to simple table
        return f"% Chart type '{chart_type}' not implemented for {title}"

def generate_latex_report(data: Dict[str, Any], title: str = "Business Analysis Report") -> str:
    """
    Generate LaTeX report from multi-agent data with graphs.

    Args:
        data: Combined data from multiple agents
        title: Report title

    Returns:
        LaTeX document string
    """
    latex_content = f"""\\documentclass{{article}}
\\usepackage{{geometry}}
\\usepackage{{graphicx}}
\\usepackage{{booktabs}}
\\usepackage{{amsmath}}
\\usepackage{{pgfplots}}
\\usepackage{{tikz}}
\\pgfplotsset{{compat=1.18}}

\\geometry{{margin=1in}}

\\title{{{title}}}
\\author{{Tally Multi-Agent System}}
\\date{{\\today}}

\\begin{{document}}

\\maketitle

\\section{{Executive Summary}}
This report presents comprehensive business analysis generated by the Tally Multi-Agent System.

"""

    # Add financial section if available
    if 'financial' in data:
        latex_content += """
\\section{Financial Analysis}
"""
        financial_data = data['financial']
        if 'summary' in financial_data:
            summary = financial_data['summary']
            latex_content += f"""
\\subsection{{Financial Summary}}
\\begin{{itemize}}
\\item Total Revenue: ₹{summary.get('total_revenue', 'N/A')}
\\item Total Expenses: ₹{summary.get('total_expenses', 'N/A')}
\\item Net Profit: ₹{summary.get('net_profit', 'N/A')}
\\item Profit Margin: {summary.get('profit_margin', 'N/A')}\\%
\\end{{itemize}}
"""

        # Add financial charts if data is available
        if 'data' in financial_data and financial_data['data']:
            chart_code = generate_chart_code('line', financial_data['data'], 'Financial Trends', 'Period', 'Amount (₹)')
            latex_content += f"""
\\subsection{{Financial Trends Chart}}
{chart_code}
"""

    # Add sales section if available
    if 'sales' in data:
        latex_content += """
\\section{Sales Analysis}
"""
        sales_data = data['sales']
        if 'summary' in sales_data:
            summary = sales_data['summary']
            latex_content += f"""
\\subsection{{Sales Summary}}
\\begin{{itemize}}
\\item Total Sales: ₹{summary.get('total_sales', 'N/A')}
\\item Number of Customers: {summary.get('customer_count', 'N/A')}
\\item Average Order Value: ₹{summary.get('avg_order_value', 'N/A')}
\\end{{itemize}}
"""

        # Add sales charts if data is available
        if 'top_customers' in sales_data and sales_data['top_customers']:
            chart_code = generate_chart_code('bar', sales_data['top_customers'], 'Top Customers by Sales', 'Customers', 'Sales (₹)')
            latex_content += f"""
\\subsection{{Top Customers Chart}}
{chart_code}
"""

    # Add inventory section if available
    if 'inventory' in data:
        latex_content += """
\\section{Inventory Analysis}
"""
        inventory_data = data['inventory']
        if 'summary' in inventory_data:
            summary = inventory_data['summary']
            latex_content += f"""
\\subsection{{Inventory Summary}}
\\begin{{itemize}}
\\item Total Items: {summary.get('total_items', 'N/A')}
\\item Stock Value: ₹{summary.get('stock_value', 'N/A')}
\\item Low Stock Items: {summary.get('low_stock_count', 'N/A')}
\\end{{itemize}}
"""

        # Add inventory charts if data is available
        if 'stock_details' in inventory_data and inventory_data['stock_details']:
            chart_code = generate_chart_code('bar', inventory_data['stock_details'], 'Stock Levels by Item', 'Items', 'Quantity')
            latex_content += f"""
\\subsection{{Stock Levels Chart}}
{chart_code}
"""

    latex_content += """
\\section{Recommendations}
Based on the analysis, the following recommendations are suggested:
\\begin{enumerate}
\\item Monitor cash flow trends closely
\\item Review inventory levels for optimization
\\item Focus on high-performing customer segments
\\item Implement cost control measures where needed
\\end{enumerate}

\\end{document}
"""

    return latex_content

def compile_and_email_report(latex_content: str, recipient_email: str, subject: str = "Business Analysis Report") -> Dict[str, Any]:
    """
    Compile LaTeX to PDF and email the report.

    Args:
        latex_content: LaTeX document content
        recipient_email: Email address to send the report
        subject: Email subject line

    Returns:
        Dictionary containing compilation and email status
    """
    import tempfile
    import subprocess
    import os

    try:
        # Create temporary directory for LaTeX compilation
        with tempfile.TemporaryDirectory() as temp_dir:
            tex_file = os.path.join(temp_dir, "report.tex")
            pdf_file = os.path.join(temp_dir, "report.pdf")

            # Write LaTeX content to file
            with open(tex_file, 'w', encoding='utf-8') as f:
                f.write(latex_content)

            # Compile LaTeX to PDF (try pdflatex first, then xelatex)
            try:
                result = subprocess.run(['pdflatex', '-output-directory', temp_dir, tex_file],
                                      capture_output=True, text=True, timeout=30)
                if result.returncode != 0:
                    # Try xelatex if pdflatex fails
                    result = subprocess.run(['xelatex', '-output-directory', temp_dir, tex_file],
                                          capture_output=True, text=True, timeout=30)
            except (subprocess.TimeoutExpired, FileNotFoundError):
                return {
                    "status": "error",
                    "error_message": "LaTeX compiler not found. Please install TeX Live or MiKTeX.",
                    "latex_content": latex_content
                }

            if result.returncode == 0 and os.path.exists(pdf_file):
                # PDF compiled successfully, now email it
                try:
                    from sub_agents.email_agent.agent import send_email_with_attachment

                    email_result = send_email_with_attachment(
                        recipient_email=recipient_email,
                        subject=subject,
                        body="Please find attached the comprehensive business analysis report generated by the Tally Multi-Agent System.",
                        attachment_path=pdf_file,
                        attachment_name="business_report.pdf"
                    )

                    return {
                        "status": "success",
                        "pdf_generated": True,
                        "email_sent": email_result.get('status') == 'success',
                        "email_details": email_result,
                        "latex_content": latex_content
                    }

                except ImportError:
                    return {
                        "status": "partial",
                        "pdf_generated": True,
                        "email_sent": False,
                        "error_message": "Email agent not available for sending attachment",
                        "latex_content": latex_content
                    }
            else:
                return {
                    "status": "error",
                    "error_message": f"LaTeX compilation failed: {result.stderr}",
                    "latex_content": latex_content
                }

    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e),
            "latex_content": latex_content
        }

def get_gemini_insights(data: Dict[str, Any], query_context: str = "") -> Dict[str, Any]:
    """
    Use Gemini API to generate insights and chart suggestions for business data.

    Args:
        data: Business data from multiple agents
        query_context: Original user query for context

    Returns:
        Dictionary containing Gemini-generated insights and suggestions
    """
    try:
        import google.generativeai as genai
        import json

        # Configure Gemini API (check for API key in environment)
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            return {
                "status": "disabled",
                "message": "Gemini API key not configured. Set GEMINI_API_KEY environment variable."
            }

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')

        # Prepare data summary for Gemini
        data_summary = json.dumps(data, indent=2, default=str)

        prompt = f"""
        Analyze the following business data and provide insights:

        Original Query Context: {query_context}

        Business Data:
        {data_summary}

        Please provide:
        1. Key insights and trends from the data
        2. Recommendations for business improvement
        3. Suggestions for charts/graphs that would best visualize this data
        4. Risk factors or areas of concern
        5. Opportunities for growth

        Format your response as JSON with the following structure:
        {{
            "insights": ["insight1", "insight2", ...],
            "recommendations": ["rec1", "rec2", ...],
            "chart_suggestions": [
                {{"type": "line", "title": "Chart Title", "description": "What this chart shows"}},
                ...
            ],
            "risks": ["risk1", "risk2", ...],
            "opportunities": ["opp1", "opp2", ...]
        }}
        """

        response = model.generate_content(prompt)

        try:
            # Parse JSON response
            insights_data = json.loads(response.text)
            return {
                "status": "success",
                "insights": insights_data,
                "raw_response": response.text
            }
        except json.JSONDecodeError:
            return {
                "status": "partial",
                "raw_insights": response.text,
                "message": "Gemini provided insights but not in expected JSON format"
            }

    except ImportError:
        return {
            "status": "disabled",
            "message": "Gemini API not available. Install google-generativeai package."
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": str(e)
        }

# Create tools for the root agent
tally_data_tool = FunctionTool(get_tally_data)
intent_parser_tool = FunctionTool(parse_user_intent)
latex_report_tool = FunctionTool(generate_latex_report)
compile_email_tool = FunctionTool(compile_and_email_report)
gemini_insights_tool = FunctionTool(get_gemini_insights)
calendar_tool = FunctionTool(schedule_calendar_event)

# Lazy loading of sub-agents to avoid circular imports
def get_sub_agents():
    """Lazy load sub-agents to avoid circular import issues"""
    try:
        from sub_agents.financial_agent.agent import agent as financial_agent
        from sub_agents.inventory_agent.agent import agent as inventory_agent
        from sub_agents.sales_agent.agent import agent as sales_agent
        from sub_agents.purchase_agent.agent import agent as purchase_agent
        from sub_agents.analytics_agent.agent import agent as analytics_agent
        from sub_agents.email_agent.agent import agent as email_agent
        from sub_agents.calendar_agent.agent import agent as calendar_agent

        sub_agents = [financial_agent, inventory_agent, sales_agent, purchase_agent, analytics_agent, email_agent, calendar_agent]
        print(f"✅ Loaded {len(sub_agents)} sub-agents successfully")
        return sub_agents
    except ImportError as e:
        print(f"⚠️ Warning: Could not import sub-agents: {e}")
        return []

# Initialize sub_agents list by loading them
sub_agents = get_sub_agents()

# Define the main agent (must be named 'agent' for ADK)
agent = LlmAgent(
    model="gemini-2.0-flash",
    name="TallyMasterAgent",
    description="""
    Master agent for comprehensive Tally ERP data analysis and business intelligence.

    I am the central coordinator for all Tally-related queries including:
    - Financial analysis and reporting (delegate to Financial Agent)
    - Inventory management and tracking (delegate to Inventory Agent)
    - Sales performance analysis (delegate to Sales Agent)
    - Purchase order management (delegate to Purchase Agent)
    - Advanced analytics and forecasting (delegate to Analytics Agent)
    - Email communication and business reporting (delegate to Email Agent)
    - Calendar management and meeting scheduling (delegate to Calendar Agent)
    - General business intelligence and cross-domain analysis

    I can handle basic queries directly or delegate specialized tasks to expert sub-agents
    for detailed domain-specific analysis.
    """,
    instruction="""
    You are the TallyMasterAgent, the central coordinator for a comprehensive Tally ERP multi-agent system with intelligent routing.

    CRITICAL: ALWAYS use parse_user_intent tool FIRST to analyze user queries before taking any action.

    Your enhanced responsibilities:
    1. Parse user intent using parse_user_intent tool to determine routing strategy
    2. Route queries dynamically based on intent analysis (NO hardcoded routing)
    3. Handle calendar scheduling using schedule_calendar_event tool
    4. Generate comprehensive LaTeX reports with generate_latex_report tool
    5. Coordinate multi-agent responses for complex queries

    INTELLIGENT ROUTING WORKFLOW:
    1. ALWAYS call parse_user_intent(user_query) first
    2. Based on intent analysis, delegate to appropriate agents:
       - primary_intent='email' → Transfer to EmailAgent
       - primary_intent='calendar' → Use schedule_calendar_event OR transfer to CalendarAgent
       - primary_intent='financial' → Transfer to FinancialAgent
       - primary_intent='inventory' → Transfer to InventoryAgent
       - primary_intent='sales' → Transfer to SalesAgent
       - primary_intent='purchase' → Transfer to PurchaseAgent
       - primary_intent='analytics' → Transfer to AnalyticsAgent
    3. For multi-agent queries (requires_multi_agent=True), coordinate multiple agents
    4. Generate LaTeX reports for comprehensive analysis

    ENHANCED WORKFLOW FOR ALL QUERIES:
    1. parse_user_intent(user_query) - MANDATORY first step
    2. Based on intent, delegate to appropriate agents
    3. Collect all agent responses
    4. get_gemini_insights(combined_data, user_query) - Get AI insights
    5. generate_latex_report(combined_data) - Create professional report
    6. compile_and_email_report(latex, email) - Email PDF report if requested

    TOOL USAGE RULES:
    - parse_user_intent: MANDATORY first step for every query
    - get_tally_data: Only for simple, direct database queries
    - schedule_calendar_event: For calendar scheduling requests
    - get_gemini_insights: ALWAYS use after collecting agent data for enhanced analysis
    - generate_latex_report: ALWAYS generate for comprehensive queries
    - compile_and_email_report: Use when email delivery is requested

    DELEGATION RULES:
    - Email requests → ALWAYS transfer to EmailAgent (never handle directly)
    - Complex analysis → Transfer to specialized agents based on intent
    - Multi-domain queries → Coordinate multiple agents, then enhance with Gemini insights
    - ALL comprehensive queries → Generate LaTeX report with graphs

    UNIVERSAL REPORTING WORKFLOW:
    Every multi-agent response should include:
    1. Real-time data from specialized agents
    2. Gemini AI insights and recommendations
    3. Professional LaTeX report with relevant graphs
    4. Email delivery option for stakeholders

    NEVER use hardcoded assumptions about what tools to call. Always let intent analysis guide your decisions.
    """,
    tools=[tally_data_tool, intent_parser_tool, latex_report_tool, compile_email_tool, gemini_insights_tool, calendar_tool],
    sub_agents=sub_agents
)

# For backward compatibility with existing code
root_agent = agent
