#!/usr/bin/env python3
"""
Simple Gemini API Test
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_gemini_direct():
    """Test Gemini API directly"""
    try:
        import google.generativeai as genai
        
        api_key = os.getenv('GEMINI_API_KEY')
        print(f"API Key: {api_key[:10]}...")
        
        genai.configure(api_key=api_key)
        
        # List available models
        print("Available models:")
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"  - {model.name}")
        
        # Try different model names
        model_names = ['gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-pro']
        
        for model_name in model_names:
            try:
                print(f"\nTesting model: {model_name}")
                model = genai.GenerativeModel(model_name)
                response = model.generate_content("Hello, how are you?")
                print(f"✅ {model_name} works: {response.text[:50]}...")
                return model_name
            except Exception as e:
                print(f"❌ {model_name} failed: {e}")
        
        return None
        
    except Exception as e:
        print(f"❌ Gemini test failed: {e}")
        return None

if __name__ == "__main__":
    working_model = test_gemini_direct()
    if working_model:
        print(f"\n✅ Working model found: {working_model}")
    else:
        print("\n❌ No working model found")
